"use client";

import { Hero } from "@/components/sections/Hero";

/**
 * Hero Demo Component - Showcases the optimized Hero section
 * 英雄区演示组件 - 展示优化后的英雄区
 */
export function HeroDemo() {
  const heroData = {
    title: "Make Your AI SaaS Product in a weekend",
    subtitle: "The complete Next.js boilerplate for building profitable SaaS, packed with AI, auth, payments, i18n, newsletter, dashboard, blog, docs, blocks, themes, SEO and more.",
    description: "Everything you need to launch your SaaS quickly and efficiently. Built with modern technologies and best practices.",
    cta: {
      primary: "Get MkSaaS",
      secondary: "See Demo"
    },
    badge: {
      text: "Special GIF: 20% off",
      icon: "🔥"
    },
    socialProof: {
      text: "90+ makers ship faster with MkSaaS",
      avatarCount: 6
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Hero hero={heroData} />
    </div>
  );
}
